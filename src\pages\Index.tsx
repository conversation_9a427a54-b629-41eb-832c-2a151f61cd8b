
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import QRCodeGenerator from '@/components/QRCodeGenerator';
import AttendanceScanner from '@/components/AttendanceScanner';
import AttendanceList from '@/components/AttendanceList';
import NetworkConfig from '@/components/NetworkConfig';
import { Users, QrCode, Calendar, Settings } from 'lucide-react';
import { saveSession, getSessions, addAttendeeToSession, type Session, type Attendee } from '@/utils/localStorage';
import { processSyncData } from '@/utils/attendanceSync';
import { useToast } from '@/hooks/use-toast';

const Index = () => {
  const [sessionName, setSessionName] = useState('');
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<Attendee[]>([]);
  const { toast } = useToast();

  // Load the most recent session on component mount
  useEffect(() => {
    // Process any sync data first
    const syncData = processSyncData();
    let syncedCount = 0;

    if (syncData.length > 0) {
      syncData.forEach(checkIn => {
        const result = addAttendeeToSession(checkIn.sessionId, checkIn.attendee);
        if (result) syncedCount++;
      });

      if (syncedCount > 0) {
        toast({
          title: "Attendance Synced! 🎉",
          description: `${syncedCount} check-in(s) have been added from mobile devices.`,
        });
      }
    }

    const sessions = getSessions();
    if (sessions.length > 0) {
      // Load the most recently created session
      const latestSession = sessions.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      setCurrentSession(latestSession);
      setAttendanceRecords(latestSession.attendees);
    }
  }, []);

  const createSession = () => {
    if (sessionName.trim()) {
      const newSession: Session = {
        id: Date.now().toString(),
        name: sessionName,
        createdAt: new Date(),
        attendees: []
      };
      
      saveSession(newSession);
      setCurrentSession(newSession);
      setSessionName('');
      setAttendanceRecords([]);
    }
  };

  const addAttendee = (email: string, name: string) => {
    if (currentSession) {
      const newAttendee: Attendee = {
        email,
        name: name || email.split('@')[0],
        timestamp: new Date(),
      };
      
      const updatedSession = addAttendeeToSession(currentSession.id, newAttendee);
      
      if (updatedSession) {
        setCurrentSession(updatedSession);
        setAttendanceRecords(updatedSession.attendees);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <QrCode className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900">QR Attendance System</h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Create attendance sessions with QR codes and track check-ins in real-time
          </p>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto">
          <Tabs defaultValue="create" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8">
              <TabsTrigger value="create" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Create Session
              </TabsTrigger>
              <TabsTrigger value="scan" className="flex items-center gap-2">
                <QrCode className="h-4 w-4" />
                QR Code
              </TabsTrigger>
              <TabsTrigger value="attendance" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Attendance
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Network
              </TabsTrigger>
            </TabsList>

            {/* Create Session Tab */}
            <TabsContent value="create">
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-gray-800">Create New Attendance Session</CardTitle>
                  <CardDescription className="text-gray-600">
                    Start by creating a named session for your event or meeting
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="sessionName" className="text-sm font-medium text-gray-700">
                      Session Name
                    </Label>
                    <Input
                      id="sessionName"
                      placeholder="e.g., Team Meeting - Dec 2024"
                      value={sessionName}
                      onChange={(e) => setSessionName(e.target.value)}
                      className="h-12 text-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <Button 
                    onClick={createSession}
                    disabled={!sessionName.trim()}
                    className="w-full h-12 text-lg bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 transform hover:scale-[1.02]"
                  >
                    Create Session & Generate QR Code
                  </Button>
                  
                  {currentSession && (
                    <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-green-800 font-medium">
                        ✅ Session "{currentSession.name}" created successfully!
                      </p>
                      <p className="text-green-600 text-sm mt-1">
                        Switch to the QR Code tab to display the attendance QR code.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* QR Code Tab */}
            <TabsContent value="scan">
              {currentSession ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <QRCodeGenerator session={currentSession} />
                  <AttendanceScanner onAttendeeAdd={addAttendee} session={currentSession} />
                </div>
              ) : (
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardContent className="flex flex-col items-center justify-center py-16">
                    <QrCode className="h-20 w-20 text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold text-gray-700 mb-2">No Active Session</h3>
                    <p className="text-gray-500 text-center max-w-md">
                      Create a new attendance session first to generate a QR code for check-ins.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Attendance Tab */}
            <TabsContent value="attendance">
              <AttendanceList
                session={currentSession}
                attendanceRecords={attendanceRecords}
              />
            </TabsContent>

            {/* Network Settings Tab */}
            <TabsContent value="settings">
              <NetworkConfig />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Index;
