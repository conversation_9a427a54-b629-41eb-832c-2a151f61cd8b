import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TestTube, Send, RefreshCw } from 'lucide-react';
import { broadcastCheckIn, testSync, getDeviceId } from '@/utils/realtimeSync';
import { useToast } from '@/hooks/use-toast';

const SyncTest = () => {
  const [testName, setTestName] = useState('Test User');
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [sessionId, setSessionId] = useState('test-session-123');
  const [serverStatus, setServerStatus] = useState('unknown');
  const [deviceId, setDeviceId] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    setDeviceId(getDeviceId());
    checkServerStatus();
  }, []);

  const checkServerStatus = async () => {
    try {
      await testSync();
      setServerStatus('connected');
    } catch (error) {
      setServerStatus('disconnected');
    }
  };

  const sendTestCheckIn = async () => {
    try {
      const testAttendee = {
        name: testName,
        email: testEmail,
        timestamp: new Date()
      };

      await broadcastCheckIn(sessionId, testAttendee);
      
      toast({
        title: "Test Check-in Sent! 📤",
        description: `Sent check-in for ${testName} to sync server`,
      });
    } catch (error) {
      toast({
        title: "Test Failed ❌",
        description: "Could not send test check-in",
        variant: "destructive"
      });
    }
  };

  const testServerConnection = async () => {
    try {
      const response = await fetch('http://***********:8081/api/health');
      const data = await response.json();
      
      toast({
        title: "Server Response ✅",
        description: `Status: ${data.status}, Pending: ${data.pendingCheckIns}`,
      });
      
      setServerStatus('connected');
    } catch (error) {
      toast({
        title: "Server Unreachable ❌",
        description: "Could not connect to sync server",
        variant: "destructive"
      });
      
      setServerStatus('disconnected');
    }
  };

  const checkPendingCheckIns = async () => {
    try {
      const response = await fetch(`http://***********:8081/api/checkins/${sessionId}`);
      const data = await response.json();
      
      toast({
        title: "Pending Check-ins 📋",
        description: `Found ${data.checkIns.length} check-ins for session ${sessionId}`,
      });
    } catch (error) {
      toast({
        title: "Query Failed ❌",
        description: "Could not fetch pending check-ins",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <TestTube className="h-12 w-12 text-purple-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Sync Test Page</h1>
          </div>
          <p className="text-lg text-gray-600">
            Test cross-device attendance synchronization
          </p>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          {/* Server Status */}
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-blue-600" />
                Server Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Sync Server</p>
                  <p className="text-sm text-gray-500">http://***********:8081</p>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  serverStatus === 'connected' 
                    ? 'bg-green-100 text-green-800' 
                    : serverStatus === 'disconnected'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {serverStatus}
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button onClick={testServerConnection} variant="outline" size="sm">
                  Test Connection
                </Button>
                <Button onClick={checkPendingCheckIns} variant="outline" size="sm">
                  Check Pending
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Device Info */}
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Device Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm"><strong>Device ID:</strong> <code>{deviceId}</code></p>
                <p className="text-sm"><strong>URL:</strong> <code>{window.location.href}</code></p>
              </div>
            </CardContent>
          </Card>

          {/* Test Check-in */}
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5 text-green-600" />
                Send Test Check-in
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="testName">Name</Label>
                  <Input
                    id="testName"
                    value={testName}
                    onChange={(e) => setTestName(e.target.value)}
                    placeholder="Test User"
                  />
                </div>
                <div>
                  <Label htmlFor="testEmail">Email</Label>
                  <Input
                    id="testEmail"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="sessionId">Session ID</Label>
                <Input
                  id="sessionId"
                  value={sessionId}
                  onChange={(e) => setSessionId(e.target.value)}
                  placeholder="test-session-123"
                />
              </div>
              
              <Button 
                onClick={sendTestCheckIn}
                className="w-full bg-green-600 hover:bg-green-700"
                disabled={serverStatus !== 'connected'}
              >
                <Send className="h-4 w-4 mr-2" />
                Send Test Check-in
              </Button>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>How to Test</CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Make sure the sync server is running (check status above)</li>
                <li>Open this page on another device on the same network</li>
                <li>Send a test check-in from one device</li>
                <li>Check if it appears on the other device</li>
                <li>Use different session IDs to test multiple sessions</li>
              </ol>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SyncTest;
