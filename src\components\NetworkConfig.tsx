import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Settings, Wifi, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const NetworkConfig = () => {
  const [networkIP, setNetworkIP] = useState('***********');
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();

  const handleSave = () => {
    // In a real implementation, you might want to store this in localStorage
    // or update the configuration file
    localStorage.setItem('network_ip', networkIP);
    setIsEditing(false);
    
    toast({
      title: "Network IP Updated",
      description: `QR codes will now use ${networkIP} for network access. Refresh the page to apply changes.`,
    });
  };

  const getCurrentNetworkUrl = () => {
    const protocol = window.location.protocol;
    const port = window.location.port;
    return `${protocol}//${networkIP}:${port}`;
  };

  return (
    <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg text-gray-800">
          <Settings className="h-5 w-5 text-blue-600" />
          Network Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Network Access Setup</p>
              <p>To allow mobile devices to access the attendance system, make sure:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Your computer and mobile devices are on the same WiFi network</li>
                <li>The network IP below matches your computer's IP address</li>
                <li>Your firewall allows connections on port 8080</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="networkIP" className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <Wifi className="h-4 w-4" />
            Network IP Address
          </Label>
          {isEditing ? (
            <div className="flex gap-2">
              <Input
                id="networkIP"
                type="text"
                value={networkIP}
                onChange={(e) => setNetworkIP(e.target.value)}
                placeholder="*************"
                className="flex-1"
              />
              <Button onClick={handleSave} size="sm">
                Save
              </Button>
              <Button 
                onClick={() => setIsEditing(false)} 
                variant="outline" 
                size="sm"
              >
                Cancel
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
              <code className="text-sm font-mono text-gray-800">{networkIP}</code>
              <Button 
                onClick={() => setIsEditing(true)} 
                variant="outline" 
                size="sm"
              >
                Edit
              </Button>
            </div>
          )}
        </div>

        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
          <p className="text-green-800 text-sm">
            <strong>Current Network URL:</strong> <code className="bg-green-100 px-1 rounded">{getCurrentNetworkUrl()}</code>
          </p>
          <p className="text-green-700 text-xs mt-1">
            Mobile devices should be able to access this URL when connected to the same network.
          </p>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>How to find your network IP:</strong></p>
          <p>• Windows: Open Command Prompt and run <code className="bg-gray-100 px-1 rounded">ipconfig</code></p>
          <p>• Mac/Linux: Open Terminal and run <code className="bg-gray-100 px-1 rounded">ifconfig</code> or <code className="bg-gray-100 px-1 rounded">ip addr</code></p>
          <p>• Look for your WiFi adapter's IPv4 address (usually starts with 192.168 or 10.0)</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default NetworkConfig;
