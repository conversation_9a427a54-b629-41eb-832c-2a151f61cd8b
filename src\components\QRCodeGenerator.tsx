
import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QrCode, Download, Users } from 'lucide-react';
import QRCode from 'qrcode';
import { getNetworkUrl } from '@/utils/networkUtils';

const QRCodeGenerator = ({ session }) => {
  const qrRef = useRef(null);
  const canvasRef = useRef(null);
  const [networkUrl, setNetworkUrl] = useState('');

  useEffect(() => {
    // Detect network URL on component mount
    detectNetworkUrl();
  }, []);

  useEffect(() => {
    if (session && qrRef.current && networkUrl) {
      // Clear previous QR code
      qrRef.current.innerHTML = '';

      // Generate real QR code
      generateQRCode();
    }
  }, [session, networkUrl]);

  const detectNetworkUrl = async () => {
    try {
      const url = await getNetworkUrl();
      setNetworkUrl(url);
    } catch (error) {
      console.error('Error detecting network URL:', error);
      // Fallback to current origin
      setNetworkUrl(window.location.origin);
    }
  };

  const generateQRCode = async () => {
    try {
      // Create the attendance URL that the QR code will contain
      const attendanceUrl = `${networkUrl}/attend/${session.id}`;

      // Create canvas element
      const canvas = document.createElement('canvas');
      
      // Generate actual QR code
      await QRCode.toCanvas(canvas, attendanceUrl, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      
      // Add canvas to the DOM
      qrRef.current.appendChild(canvas);
      canvasRef.current = canvas;
    } catch (error) {
      console.error('Error generating QR code:', error);
      
      // Fallback: show error message
      const errorDiv = document.createElement('div');
      errorDiv.className = 'p-4 bg-red-50 border border-red-200 rounded text-red-700 text-center';
      errorDiv.textContent = 'Error generating QR code';
      qrRef.current.appendChild(errorDiv);
    }
  };

  const downloadQR = () => {
    if (canvasRef.current) {
      const link = document.createElement('a');
      link.download = `attendance-qr-${session.name.replace(/\s+/g, '-')}.png`;
      link.href = canvasRef.current.toDataURL();
      link.click();
    }
  };

  const attendanceUrl = networkUrl ? `${networkUrl}/attend/${session.id}` : '';

  return (
    <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2 text-xl text-gray-800">
          <QrCode className="h-6 w-6 text-blue-600" />
          Attendance QR Code
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center">
          <h3 className="font-semibold text-lg text-gray-800 mb-2">{session.name}</h3>
          <p className="text-sm text-gray-500">
            Created: {session.createdAt.toLocaleDateString()} at {session.createdAt.toLocaleTimeString()}
          </p>
        </div>
        
        <div className="flex justify-center">
          <div 
            ref={qrRef} 
            className="p-4 bg-white rounded-lg shadow-inner border-2 border-gray-100"
          />
        </div>
        
        <div className="space-y-3">
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm text-gray-600 mb-1">Attendance URL:</p>
            <code className="text-xs text-blue-600 break-all">{attendanceUrl}</code>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>Current Attendees: {session.attendees.length}</span>
            </div>
          </div>
          
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <p className="text-green-800 text-sm">
              📱 <strong>How to use:</strong> Open any QR code scanner app on your phone and point it at the QR code above. It will automatically open the attendance page where you can check in with your name or email.
            </p>
          </div>
        </div>
        
        <Button 
          onClick={downloadQR}
          className="w-full bg-green-600 hover:bg-green-700 text-white transition-colors duration-200"
        >
          <Download className="h-4 w-4 mr-2" />
          Download QR Code
        </Button>
      </CardContent>
    </Card>
  );
};

export default QRCodeGenerator;
