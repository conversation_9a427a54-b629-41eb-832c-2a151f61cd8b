import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, User, Clock, ArrowLeft } from 'lucide-react';

const CheckInSuccess = () => {
  const { sessionId } = useParams();
  const [searchParams] = useSearchParams();
  const [attendeeName, setAttendeeName] = useState('');
  const [sessionName, setSessionName] = useState('');
  const [checkInTime, setCheckInTime] = useState('');

  useEffect(() => {
    // Get data from URL parameters
    const name = searchParams.get('name') || 'Guest';
    const session = searchParams.get('session') || 'Unknown Session';
    const time = searchParams.get('time') || new Date().toLocaleString();
    
    setAttendeeName(name);
    setSessionName(session);
    setCheckInTime(time);
  }, [searchParams]);

  const goBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900">Check-in Successful!</h1>
          </div>
          <p className="text-lg text-gray-600">
            Your attendance has been recorded successfully.
          </p>
        </div>

        {/* Success Card */}
        <div className="max-w-md mx-auto">
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm border-green-200">
            <CardHeader className="text-center bg-green-50">
              <CardTitle className="text-2xl text-green-800">Welcome!</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="text-center">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-10 w-10 text-green-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800 mb-2">{attendeeName}</h2>
                <p className="text-gray-600">has been checked in to</p>
                <p className="text-lg font-medium text-blue-800">{sessionName}</p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-2" />
                  <span>Check-in time: {checkInTime}</span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                  <p className="text-green-800 text-sm text-center">
                    ✅ <strong>You're all set!</strong> Your attendance has been recorded and you can now close this page.
                  </p>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                  <p className="text-blue-800 text-sm text-center">
                    📱 <strong>Note:</strong> Your check-in data is stored locally on the session organizer's device.
                  </p>
                </div>
              </div>

              <Button 
                onClick={goBack}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Additional Info */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Thank you for participating!</p>
          <p>If you have any questions, please contact the session organizer.</p>
        </div>
      </div>
    </div>
  );
};

export default CheckInSuccess;
