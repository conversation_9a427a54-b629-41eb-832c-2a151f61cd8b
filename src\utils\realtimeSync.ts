/**
 * Real-time synchronization for cross-device attendance
 * Uses BroadcastChannel API and localStorage events for same-network sync
 */

import { type Attendee, addAttendeeToSession, getSessions } from './localStorage';

// Broadcast channel for real-time communication
const SYNC_CHANNEL = 'attendance_sync';
let broadcastChannel: BroadcastChannel | null = null;

export interface SyncMessage {
  type: 'NEW_CHECKIN' | 'PING' | 'PONG';
  sessionId: string;
  attendee?: Attendee;
  timestamp: number;
  deviceId: string;
}

/**
 * Initialize real-time sync
 */
export const initializeRealtimeSync = (onSyncReceived?: (message: SyncMessage) => void): void => {
  // Check if BroadcastChannel is supported
  if (typeof BroadcastChannel !== 'undefined') {
    try {
      broadcastChannel = new BroadcastChannel(SYNC_CHANNEL);
      
      broadcastChannel.onmessage = (event) => {
        const message: SyncMessage = event.data;
        
        // Don't process messages from the same device
        if (message.deviceId === getDeviceId()) return;
        
        console.log('Received sync message:', message);
        
        if (message.type === 'NEW_CHECKIN' && message.attendee) {
          // Process the new check-in
          const result = addAttendeeToSession(message.sessionId, message.attendee);
          if (result && onSyncReceived) {
            onSyncReceived(message);
          }
        }
      };
      
      console.log('Real-time sync initialized');
    } catch (error) {
      console.error('Failed to initialize BroadcastChannel:', error);
    }
  }
  
  // Also listen for localStorage changes as fallback
  window.addEventListener('storage', handleStorageChange);
};

/**
 * Broadcast a new check-in to other devices
 */
export const broadcastCheckIn = (sessionId: string, attendee: Attendee): void => {
  const message: SyncMessage = {
    type: 'NEW_CHECKIN',
    sessionId,
    attendee,
    timestamp: Date.now(),
    deviceId: getDeviceId()
  };
  
  // Broadcast via BroadcastChannel
  if (broadcastChannel) {
    try {
      broadcastChannel.postMessage(message);
      console.log('Broadcasted check-in:', message);
    } catch (error) {
      console.error('Failed to broadcast message:', error);
    }
  }
  
  // Also store in a shared localStorage key for cross-tab sync
  try {
    const sharedKey = `sync_checkin_${Date.now()}_${Math.random()}`;
    localStorage.setItem(sharedKey, JSON.stringify(message));
    
    // Clean up after a short delay
    setTimeout(() => {
      localStorage.removeItem(sharedKey);
    }, 5000);
  } catch (error) {
    console.error('Failed to store sync message:', error);
  }
};

/**
 * Handle localStorage changes (fallback sync method)
 */
const handleStorageChange = (event: StorageEvent): void => {
  if (event.key && event.key.startsWith('sync_checkin_') && event.newValue) {
    try {
      const message: SyncMessage = JSON.parse(event.newValue);
      
      // Don't process messages from the same device
      if (message.deviceId === getDeviceId()) return;
      
      if (message.type === 'NEW_CHECKIN' && message.attendee) {
        console.log('Received storage sync message:', message);
        addAttendeeToSession(message.sessionId, message.attendee);
        
        // Trigger a custom event for components to listen to
        window.dispatchEvent(new CustomEvent('attendanceSync', { 
          detail: message 
        }));
      }
    } catch (error) {
      console.error('Failed to process storage sync message:', error);
    }
  }
};

/**
 * Get or create a unique device ID
 */
export const getDeviceId = (): string => {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = 'device_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
};

/**
 * Clean up sync resources
 */
export const cleanupRealtimeSync = (): void => {
  if (broadcastChannel) {
    broadcastChannel.close();
    broadcastChannel = null;
  }
  
  window.removeEventListener('storage', handleStorageChange);
};

/**
 * Test if real-time sync is working
 */
export const testSync = (): void => {
  const testMessage: SyncMessage = {
    type: 'PING',
    sessionId: 'test',
    timestamp: Date.now(),
    deviceId: getDeviceId()
  };
  
  if (broadcastChannel) {
    broadcastChannel.postMessage(testMessage);
    console.log('Sent test ping');
  }
};
