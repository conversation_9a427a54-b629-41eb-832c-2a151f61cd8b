# Network Access Setup for QR Attendance System

## Overview
This guide helps you set up network access so mobile devices can scan QR codes and check in to attendance sessions.

## Quick Setup

### 1. Find Your Network IP Address

**Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" under your WiFi adapter (usually starts with 192.168 or 10.0)

**Mac/Linux:**
```bash
ifconfig
# or
ip addr show
```
Look for your WiFi interface's inet address

### 2. Update Network Configuration

1. Open the attendance system in your browser: `http://localhost:8080`
2. Go to the **Network** tab
3. Click **Edit** next to the Network IP Address
4. Enter your computer's IP address (found in step 1)
5. Click **Save**

### 3. Test Network Access

1. Create a new attendance session
2. Go to the **QR Code** tab
3. The QR code should now contain your network IP (e.g., `http://*************:8080/attend/...`)
4. Try accessing the URL from your mobile device's browser

## Troubleshooting

### Mobile devices can't access the system

**Check WiFi Connection:**
- Ensure your computer and mobile devices are on the same WiFi network
- Guest networks often block device-to-device communication

**Check Firewall:**
- Windows: Allow port 8080 through Windows Firewall
- Mac: System Preferences > Security & Privacy > Firewall > Firewall Options > Allow port 8080
- Linux: `sudo ufw allow 8080`

**Check Network IP:**
- Make sure the IP in the Network tab matches your computer's actual IP
- IP addresses can change when you reconnect to WiFi

### QR codes still show localhost

- Make sure you've updated the Network IP in the Network tab
- Refresh the page after changing the network IP
- Clear your browser cache if needed

## Network IP Examples

- **Home WiFi:** Usually `192.168.1.x` or `192.168.0.x`
- **Office WiFi:** Often `10.0.x.x` or `172.16.x.x`
- **Mobile Hotspot:** Typically `192.168.43.x`

## Security Notes

- The system only works on your local network
- No data is sent to external servers
- All attendance data stays on your computer
- The system is only accessible while your computer is running

## Default Configuration

The system comes pre-configured with IP `***********` which matches the Vite development server output. Update this to match your actual network IP for mobile access.
