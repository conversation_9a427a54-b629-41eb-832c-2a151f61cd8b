
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UserPlus, Mail, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { storePendingCheckIn, createSyncLink } from '@/utils/attendanceSync';
import { getNetworkUrlSync } from '@/utils/networkUtils';

const AttendanceScanner = ({ onAttendeeAdd, session }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const validateEmail = (email) => {
    // Basic email validation - accepts any valid email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Require either email or name
    if (!email.trim() && !name.trim()) {
      toast({
        title: "Information Required",
        description: "Please enter either your email address or your name",
        variant: "destructive"
      });
      return;
    }

    // If email is provided, validate it
    if (email.trim() && !validateEmail(email)) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive"
      });
      return;
    }

    // Check if already checked in (by email if provided, otherwise by name)
    const identifier = email.trim() || name.trim();
    const alreadyCheckedIn = session.attendees.some(attendee =>
      (email.trim() && attendee.email === email) ||
      (!email.trim() && attendee.name === name)
    );

    if (alreadyCheckedIn) {
      toast({
        title: "Already Checked In",
        description: `${identifier} has already been recorded for this session`,
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Use email or name as fallback
      const finalEmail = email.trim() || '';
      const finalName = name.trim() || (email.trim() ? email.split('@')[0] : '');

      // Check if this is a cross-device check-in (from QR code scan)
      const urlParams = new URLSearchParams(window.location.search);
      const isFromQRCode = urlParams.has('sessionData');

      if (isFromQRCode) {
        // For mobile users: store the check-in for sync and create sync link
        const newAttendee = {
          email: finalEmail,
          name: finalName,
          timestamp: new Date(),
        };

        storePendingCheckIn(session.id, newAttendee);

        // Create sync link for the organizer
        const baseUrl = getNetworkUrlSync();
        const syncLink = createSyncLink(baseUrl);

        // Redirect to success page with sync link
        const successUrl = `/success/${session.id}?name=${encodeURIComponent(finalName)}&session=${encodeURIComponent(session.name)}&time=${encodeURIComponent(new Date().toLocaleString())}&syncLink=${encodeURIComponent(syncLink)}`;
        navigate(successUrl);
      } else {
        // For local users: add directly to session
        onAttendeeAdd(finalEmail, finalName);

        // Show toast for local users
        toast({
          title: "Check-in Successful! 🎉",
          description: `Welcome ${finalName}! Your attendance has been recorded.`,
        });

        // Clear form
        setEmail('');
        setName('');
      }
    } catch (error) {
      toast({
        title: "Check-in Failed",
        description: "Please try again",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2 text-xl text-gray-800">
          <UserPlus className="h-6 w-6 text-green-600" />
          Attendance Check-in
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-blue-800 font-medium">Checking in for:</p>
            <p className="text-blue-900 text-lg font-semibold">{session.name}</p>
          </div>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <Mail className="h-4 w-4" />
                Email Address (Optional)
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="h-12 text-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <User className="h-4 w-4" />
                Full Name
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Your full name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="h-12 text-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <Button
            type="submit"
            disabled={isSubmitting || (!email.trim() && !name.trim())}
            className="w-full h-12 text-lg bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Checking In...
              </div>
            ) : (
              <>
                <UserPlus className="h-5 w-5 mr-2" />
                Check In to Session
              </>
            )}
          </Button>
          
          <div className="text-xs text-gray-500 text-center space-y-1">
            <p>• Provide either your email address or full name</p>
            <p>• Each person can only check in once per session</p>
            <p>• Your information is used only for attendance tracking</p>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default AttendanceScanner;
